'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, Leaf } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navItems = [
    { name: 'How It Works', href: '#how-it-works' },
    { name: 'Features', href: '#features' },
    { name: 'Impact', href: '#impact' },
    { name: 'Community', href: '#community' },
  ];

  return (
    <header
      className={`fixed top-4 left-4 right-4 z-50 transition-all-smooth rounded-2xl ${
        isScrolled
          ? 'bg-brand-white/95 backdrop-blur-md shadow-xl border border-gray-200'
          : 'bg-brand-dark-green/90 backdrop-blur-sm border border-brand-pine-green/30'
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-10">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Leaf className={`h-9 w-9 transition-all-smooth ${
                isScrolled ? 'text-brand-pine-green' : 'text-brand-pine-green'
              } group-hover:scale-110 drop-shadow-sm`} />
              <div className="absolute inset-0 bg-brand-caribbean-green/20 rounded-full scale-0 group-hover:scale-150 transition-transform duration-300"></div>
            </div>
            <span className={`text-2xl font-heading transition-all-smooth ${
              isScrolled ? 'text-brand-dark-green' : 'text-brand-anti-flash-white'
            }`}>
              Pedi
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className={`text-sm font-subheading transition-all-smooth hover:scale-105 relative group ${
                  isScrolled
                    ? 'text-brand-basil hover:text-brand-forest'
                    : 'text-brand-anti-flash-white/90 hover:text-brand-anti-flash-white'
                }`}
              >
                {item.name}
                <span className={`absolute -bottom-1 left-0 w-0 h-0.5 transition-all duration-300 group-hover:w-full ${
                  isScrolled ? 'bg-brand-forest' : 'bg-brand-caribbean-green'
                }`}></span>
              </a>
            ))}
          </nav>

          {/* Desktop CTA Buttons */}
          <div className="hidden lg:flex items-center space-x-4">
            <Link href="/auth">
              <Button
                variant="ghost"
                className={`font-subheading transition-all-smooth rounded-xl px-6 ${
                  isScrolled
                    ? 'text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-pine-green/10'
                    : 'text-brand-white hover:text-brand-white hover:bg-brand-white/10'
                }`}
              >
                Sign In
              </Button>
            </Link>
            <Link href="/auth">
              <Button
                className={`font-subheading transition-all-smooth hover-lift rounded-xl px-6 shadow-lg ${
                  isScrolled
                    ? 'bg-brand-pine-green hover:bg-brand-dark-green text-brand-white hover:shadow-brand-pine-green/25'
                    : 'bg-brand-pine-green text-brand-white hover:bg-brand-dark-green hover:shadow-brand-pine-green/25'
                }`}
              >
                Get Started
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className={`lg:hidden p-3 rounded-xl transition-all-smooth ${
              isScrolled
                ? 'text-brand-dark-green hover:bg-brand-pine-green/10'
                : 'text-brand-white hover:bg-brand-white/10'
            }`}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-4 pt-4 pb-6 space-y-2 bg-brand-white/95 backdrop-blur-md rounded-xl mt-4 shadow-xl border border-gray-200 animate-fade-in-up">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-3 font-subheading text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-pine-green/10 rounded-lg transition-all-smooth"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <div className="pt-4 space-y-3 border-t border-gray-200">
                <Link href="/auth" className="block">
                  <Button
                    variant="ghost"
                    className="w-full font-subheading text-brand-dark-green hover:text-brand-pine-green hover:bg-brand-pine-green/10 rounded-lg"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth" className="block">
                  <Button
                    className="w-full font-subheading bg-brand-pine-green hover:bg-brand-dark-green text-brand-white rounded-lg shadow-lg hover:shadow-brand-pine-green/25"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
